<template>
  <div class="article-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon><Document /></el-icon>
            文章管理
          </h1>
          <p class="page-description">
            管理博客文章、发布状态和内容编辑
            <el-tooltip content="快捷键：Ctrl+N 新建文章，Ctrl+E 导出文章，F5 刷新列表" placement="bottom">
              <el-icon class="help-icon"><QuestionFilled /></el-icon>
            </el-tooltip>
          </p>
        </div>
        <div class="header-actions">
          <el-button
            type="primary"
            :icon="Plus"
            @click="handleCreateArticle"
            v-permission="'article.create'"
          >
            新建文章
          </el-button>
          <el-button
            :icon="Download"
            @click="handleExportArticles"
            v-permission="'article.list'"
          >
            导出文章
          </el-button>
          <el-button
            :icon="Upload"
            @click="handleImportArticles"
            v-permission="'article.create'"
          >
            导入文章
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="16">
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon total">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ articleStats.totalArticles || 0 }}</div>
                <div class="stats-label">总文章数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon published">
                <el-icon><Check /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ articleStats.publishedArticles || 0 }}</div>
                <div class="stats-label">已发布</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon draft">
                <el-icon><Edit /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ articleStats.draftArticles || 0 }}</div>
                <div class="stats-label">草稿</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon today">
                <el-icon><Plus /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ articleStats.todayArticles || 0 }}</div>
                <div class="stats-label">今日新增</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 筛选和搜索区域 -->
    <el-card class="filter-card" shadow="never">
      <div class="filter-content">
        <el-form :model="filterForm" inline class="filter-form">
          <el-form-item label="搜索">
            <el-input
              v-model="filterForm.search"
              placeholder="搜索文章标题或内容"
              :prefix-icon="Search"
              clearable
              @input="handleSearch"
              style="width: 240px"
            />
          </el-form-item>

          <el-form-item label="状态">
            <el-select
              v-model="filterForm.status"
              placeholder="选择状态"
              clearable
              @change="handleFilter"
              style="width: 120px"
            >
              <el-option label="已发布" value="published" />
              <el-option label="草稿" value="draft" />
            </el-select>
          </el-form-item>

          <el-form-item label="分类">
            <el-select
              v-model="filterForm.categoryId"
              placeholder="选择分类"
              clearable
              @change="handleFilter"
              style="width: 140px"
            >
              <el-option
                v-for="category in categories"
                :key="category.id"
                :label="category.name"
                :value="category.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="标签">
            <el-select
              v-model="filterForm.tag"
              placeholder="选择标签"
              clearable
              @change="handleFilter"
              style="width: 140px"
            >
              <el-option
                v-for="tag in tags"
                :key="tag.id"
                :label="tag.name"
                :value="tag.name"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="排序">
            <el-select
              v-model="filterForm.orderBy"
              @change="handleFilter"
              style="width: 120px"
            >
              <el-option label="创建时间" value="createdAt" />
              <el-option label="更新时间" value="updatedAt" />
              <el-option label="发布时间" value="publishedAt" />
              <el-option label="标题" value="title" />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-select
              v-model="filterForm.orderDirection"
              @change="handleFilter"
              style="width: 80px"
            >
              <el-option label="降序" value="DESC" />
              <el-option label="升序" value="ASC" />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button @click="handleResetFilter">重置</el-button>
          </el-form-item>
        </el-form>

        <!-- 批量操作 -->
        <div v-if="selectedArticles && selectedArticles.length > 0" class="batch-actions">
          <span class="batch-info">已选择 {{ selectedArticles.length }} 篇文章</span>
          <el-button
            type="danger"
            size="small"
            :icon="Delete"
            @click="handleBatchDelete"
            v-permission="'article.delete'"
          >
            批量删除
          </el-button>
          <el-button
            type="success"
            size="small"
            :icon="Check"
            @click="handleBatchPublish"
            v-permission="'article.publish'"
          >
            批量发布
          </el-button>
          <el-button
            size="small"
            :icon="Edit"
            @click="handleBatchUnpublish"
            v-permission="'article.publish'"
          >
            批量取消发布
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 文章列表 -->
    <el-card class="table-card" shadow="never">
      <div v-loading="loading" class="table-container">
        <el-table
          :data="articles"
          @selection-change="handleSelectionChange"
          stripe
          class="article-table"
        >
          <el-table-column
            type="selection"
            width="55"
            v-if="hasPermission('article.delete') || hasPermission('article.update')"
          />

          <el-table-column label="文章信息" min-width="300">
            <template #default="{ row }">
              <div class="article-info">
                <div class="article-details">
                  <div class="article-title">
                    <el-button
                      type="text"
                      @click="handleViewArticle(row)"
                      class="title-link"
                    >
                      {{ row.title }}
                    </el-button>
                    <el-tag
                      v-if="row.status === 'published'"
                      size="small"
                      type="success"
                      class="status-tag"
                    >
                      已发布
                    </el-tag>
                    <el-tag
                      v-else
                      size="small"
                      type="warning"
                      class="status-tag"
                    >
                      草稿
                    </el-tag>
                  </div>
                  <div class="article-excerpt" v-if="row.excerpt">
                    {{ row.excerpt }}
                  </div>
                  <div class="article-meta">
                    <span class="meta-item">
                      <el-icon><User /></el-icon>
                      作者: {{ row.author?.username || '未知' }}
                    </span>
                    <span class="meta-item" v-if="row.category">
                      <el-icon><Folder /></el-icon>
                      分类: {{ row.category.name }}
                    </span>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="标签" width="200">
            <template #default="{ row }">
              <div class="tags-container">
                <el-tag
                  v-for="tag in (row.tags || [])"
                  :key="tag.id"
                  size="small"
                  class="tag-item"
                >
                  {{ tag.name }}
                </el-tag>
                <span v-if="!row.tags || row.tags.length === 0" class="no-tags">
                  暂无标签
                </span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag
                :type="row.status === 'published' ? 'success' : 'warning'"
                size="small"
              >
                {{ row.status === 'published' ? '已发布' : '草稿' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="发布时间" width="160" show-overflow-tooltip>
            <template #default="{ row }">
              <div v-if="row.publishedAt" class="publish-time">
                <el-icon><Clock /></el-icon>
                {{ formatDate(row.publishedAt) }}
              </div>
              <span v-else class="not-published">未发布</span>
            </template>
          </el-table-column>

          <el-table-column label="创建时间" width="160" show-overflow-tooltip>
            <template #default="{ row }">
              <div class="create-time">
                <el-icon><Calendar /></el-icon>
                {{ formatDate(row.createdAt) }}
              </div>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="240" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button
                  type="text"
                  size="small"
                  :icon="View"
                  @click="handleViewArticle(row)"
                  v-permission="'article.read'"
                >
                  查看
                </el-button>
                <el-button
                  type="text"
                  size="small"
                  :icon="Edit"
                  @click="handleEditArticle(row)"
                  v-permission="'article.update'"
                >
                  编辑
                </el-button>

                <el-dropdown trigger="click" v-permission="'article.update'">
                  <el-button type="text" size="small">
                    更多
                    <el-icon class="el-icon--right"><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item
                        v-if="row.status === 'draft'"
                        @click="handlePublishArticle(row)"
                        v-permission="'article.publish'"
                      >
                        <el-icon><Check /></el-icon>
                        发布文章
                      </el-dropdown-item>
                      <el-dropdown-item
                        v-else
                        @click="handleUnpublishArticle(row)"
                        v-permission="'article.publish'"
                      >
                        <el-icon><Close /></el-icon>
                        取消发布
                      </el-dropdown-item>
                      <el-dropdown-item
                        @click="handleDuplicateArticle(row)"
                        v-permission="'article.create'"
                      >
                        <el-icon><CopyDocument /></el-icon>
                        复制文章
                      </el-dropdown-item>
                      <el-dropdown-item
                        divided
                        @click="handleDeleteArticle(row)"
                        v-permission="'article.delete'"
                        class="danger-item"
                      >
                        <el-icon><Delete /></el-icon>
                        删除文章
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 空状态 -->
        <div v-if="!loading && articles.length === 0" class="empty-state">
          <el-empty
            :description="getEmptyDescription()"
            :image-size="120"
          >
            <template #image>
              <el-icon size="120" color="#c0c4cc">
                <Document />
              </el-icon>
            </template>
            <el-button
              v-if="!hasActiveFilters"
              type="primary"
              :icon="Plus"
              @click="handleCreateArticle"
              v-permission="'article.create'"
            >
              创建第一篇文章
            </el-button>
            <div v-else class="empty-actions">
              <el-button @click="handleResetFilter">
                清除筛选条件
              </el-button>
              <el-button
                type="primary"
                :icon="Plus"
                @click="handleCreateArticle"
                v-permission="'article.create'"
              >
                新建文章
              </el-button>
            </div>
          </el-empty>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-container" v-if="articles.length > 0">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Document,
  Plus,
  Download,
  Upload,
  Search,
  Check,
  Edit,
  Delete,
  View,
  Close,
  User,
  Folder,
  Clock,
  Calendar,
  ArrowDown,
  CopyDocument,
  QuestionFilled
} from '@element-plus/icons-vue'

// 导入stores和services
import { useArticleStore } from '@/stores/article'
import { useCategoryStore } from '@/stores/category'
import { useTagStore } from '@/stores/tag'
import { usePermission } from '@/composables/usePermission'
import { formatDate } from '@/utils/date'
import type { Article } from '@/types/article'

// 初始化
const router = useRouter()
const articleStore = useArticleStore()
const categoryStore = useCategoryStore()
const tagStore = useTagStore()
const { hasPermission } = usePermission()

// 响应式数据
const loading = ref(false)
const selectedArticles = ref<Article[]>([])

// 筛选表单
const filterForm = ref({
  search: '',
  status: '' as '' | 'draft' | 'published',
  categoryId: '' as string | number | '',
  tag: '',
  orderBy: 'createdAt',
  orderDirection: 'DESC' as 'ASC' | 'DESC'
})

// 分页数据
const currentPage = ref(1)
const pageSize = ref(10)

// 计算属性
const articles = computed(() => articleStore.articles)
const total = computed(() => articleStore.pagination.totalItems)
const categories = computed(() => categoryStore.categories)
const tags = computed(() => tagStore.tags)

// 检查是否有活跃的筛选条件
const hasActiveFilters = computed(() => {
  return !!(
    filterForm.value.search ||
    filterForm.value.status ||
    filterForm.value.categoryId ||
    filterForm.value.tag
  )
})

// 获取空状态描述
const getEmptyDescription = () => {
  if (hasActiveFilters.value) {
    return '没有找到符合筛选条件的文章'
  }
  return '还没有任何文章，开始创建你的第一篇文章吧！'
}

// 文章统计数据
const articleStats = computed(() => {
  const totalArticles = articleStore.pagination.totalItems || articles.value.length
  const publishedArticles = articles.value.filter(a => a.status === 'published').length
  const draftArticles = articles.value.filter(a => a.status === 'draft').length
  const today = new Date().toDateString()
  const todayArticles = articles.value.filter(a =>
    new Date(a.createdAt).toDateString() === today
  ).length

  return {
    totalArticles,
    publishedArticles,
    draftArticles,
    todayArticles
  }
})

// 快速操作
const handleQuickPublish = async (article: Article) => {
  try {
    if (article.status === 'draft') {
      await articleStore.publishArticle(article.id)
      ElMessage.success('文章发布成功')
    } else {
      await articleStore.unpublishArticle(article.id)
      ElMessage.success('取消发布成功')
    }
  } catch (error) {
    console.error('快速操作失败:', error)
    ElMessage.error('操作失败')
  }
}

const handleDuplicateArticle = async (article: Article) => {
  try {
    const duplicateData = {
      title: `${article.title} - 副本`,
      content: article.content,
      excerpt: article.excerpt,
      status: 'draft' as const,
      categoryId: article.categoryId
    }

    await articleStore.createArticle(duplicateData)
    ElMessage.success('文章复制成功')
    fetchArticles()
  } catch (error) {
    console.error('复制文章失败:', error)
    ElMessage.error('复制文章失败')
  }
}

// 防抖搜索
let searchTimeout: NodeJS.Timeout
const handleSearch = () => {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    currentPage.value = 1
    fetchArticles()
  }, 300)
}

// 筛选处理
const handleFilter = () => {
  currentPage.value = 1
  fetchArticles()
}

// 重置筛选
const handleResetFilter = () => {
  filterForm.value = {
    search: '',
    status: '',
    categoryId: '',
    tag: '',
    orderBy: 'createdAt',
    orderDirection: 'DESC'
  }
  currentPage.value = 1
  fetchArticles()
}

// 获取文章列表
const fetchArticles = async (showLoading = true) => {
  try {
    if (showLoading) {
      loading.value = true
    }

    await articleStore.fetchArticles(
      currentPage.value,
      pageSize.value,
      {
        search: filterForm.value.search || undefined,
        status: filterForm.value.status || undefined,
        category: filterForm.value.categoryId || undefined,
        tag: filterForm.value.tag || undefined
      }
    )
  } catch (error: any) {
    console.error('获取文章列表失败:', error)

    // 根据错误类型显示不同的错误信息
    if (error.response?.status === 403) {
      ElMessage.error('没有权限访问文章列表')
    } else if (error.response?.status === 401) {
      ElMessage.error('登录已过期，请重新登录')
      // 可以在这里触发重新登录逻辑
    } else if (error.code === 'NETWORK_ERROR') {
      ElMessage.error('网络连接失败，请检查网络设置')
    } else {
      ElMessage.error(error.message || '获取文章列表失败')
    }
  } finally {
    if (showLoading) {
      loading.value = false
    }
  }
}

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchArticles()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchArticles()
}

// 选择处理
const handleSelectionChange = (selection: Article[]) => {
  selectedArticles.value = selection
}

// 文章操作
const handleCreateArticle = () => {
  router.push('/admin/articles/new')
}

const handleViewArticle = (article: Article) => {
  // 可以跳转到前台文章详情页面或者后台预览页面
  window.open(`/article/${article.id}`, '_blank')
}

const handleEditArticle = (article: Article) => {
  router.push(`/admin/articles/${article.id}/edit`)
}

const handleDeleteArticle = async (article: Article) => {
  try {
    await articleStore.deleteArticle(article.id)
    ElMessage.success('文章删除成功')
    fetchArticles()
  } catch (error) {
    console.error('删除文章失败:', error)
    ElMessage.error('删除文章失败')
  }
}

const handlePublishArticle = async (article: Article) => {
  try {
    await articleStore.publishArticle(article.id)
    ElMessage.success('文章发布成功')
    fetchArticles()
  } catch (error) {
    console.error('发布文章失败:', error)
    ElMessage.error('发布文章失败')
  }
}

const handleUnpublishArticle = async (article: Article) => {
  try {
    await articleStore.unpublishArticle(article.id)
    ElMessage.success('取消发布成功')
    fetchArticles()
  } catch (error) {
    console.error('取消发布失败:', error)
    ElMessage.error('取消发布失败')
  }
}

// 批量操作
const handleBatchDelete = async () => {
  if (selectedArticles.value.length === 0) {
    ElMessage.warning('请选择要删除的文章')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedArticles.value.length} 篇文章吗？`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const ids = selectedArticles.value.map(article => article.id)
    await articleStore.deleteArticles(ids)
    ElMessage.success('批量删除成功')
    selectedArticles.value = []
    fetchArticles()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

const handleBatchPublish = async () => {
  if (selectedArticles.value.length === 0) {
    ElMessage.warning('请选择要发布的文章')
    return
  }

  try {
    const draftArticles = selectedArticles.value.filter(a => a.status === 'draft')
    if (draftArticles.length === 0) {
      ElMessage.warning('选中的文章中没有草稿状态的文章')
      return
    }

    for (const article of draftArticles) {
      await articleStore.publishArticle(article.id)
    }

    ElMessage.success(`成功发布 ${draftArticles.length} 篇文章`)
    selectedArticles.value = []
    fetchArticles()
  } catch (error) {
    console.error('批量发布失败:', error)
    ElMessage.error('批量发布失败')
  }
}

const handleBatchUnpublish = async () => {
  if (selectedArticles.value.length === 0) {
    ElMessage.warning('请选择要取消发布的文章')
    return
  }

  try {
    const publishedArticles = selectedArticles.value.filter(a => a.status === 'published')
    if (publishedArticles.length === 0) {
      ElMessage.warning('选中的文章中没有已发布状态的文章')
      return
    }

    for (const article of publishedArticles) {
      await articleStore.unpublishArticle(article.id)
    }

    ElMessage.success(`成功取消发布 ${publishedArticles.length} 篇文章`)
    selectedArticles.value = []
    fetchArticles()
  } catch (error) {
    console.error('批量取消发布失败:', error)
    ElMessage.error('批量取消发布失败')
  }
}

// 导入导出
const handleExportArticles = async () => {
  try {
    // 获取当前筛选条件下的所有文章
    const params = {
      search: filterForm.value.search || undefined,
      status: filterForm.value.status || undefined,
      category: filterForm.value.categoryId || undefined,
      tag: filterForm.value.tag || undefined,
      limit: 1000 // 导出大量数据
    }

    const response = await articleStore.fetchArticles(1, 1000, params)
    const articles = response.articles

    if (articles.length === 0) {
      ElMessage.warning('没有可导出的文章')
      return
    }

    // 准备导出数据
    const exportData = articles.map(article => ({
      标题: article.title,
      内容: article.content,
      摘要: article.excerpt || '',
      状态: article.status === 'published' ? '已发布' : '草稿',
      分类: article.category?.name || '',
      标签: article.tags?.map(tag => tag.name).join(', ') || '',
      创建时间: formatDate(article.createdAt),
      发布时间: article.publishedAt ? formatDate(article.publishedAt) : ''
    }))

    // 创建CSV内容
    const headers = Object.keys(exportData[0])
    const csvContent = [
      headers.join(','),
      ...exportData.map(row =>
        headers.map(header => `"${String(row[header as keyof typeof row]).replace(/"/g, '""')}"`).join(',')
      )
    ].join('\n')

    // 下载文件
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `articles_${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    ElMessage.success(`成功导出 ${articles.length} 篇文章`)
  } catch (error) {
    console.error('导出文章失败:', error)
    ElMessage.error('导出文章失败')
  }
}

const handleImportArticles = () => {
  // 创建文件输入元素
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.csv,.json'
  input.onchange = async (event) => {
    const file = (event.target as HTMLInputElement).files?.[0]
    if (!file) return

    try {
      const text = await file.text()
      let importData: any[] = []

      if (file.name.endsWith('.csv')) {
        // 解析CSV
        const lines = text.split('\n')
        const headers = lines[0].split(',').map(h => h.replace(/"/g, ''))

        for (let i = 1; i < lines.length; i++) {
          if (lines[i].trim()) {
            const values = lines[i].split(',').map(v => v.replace(/"/g, ''))
            const row: any = {}
            headers.forEach((header, index) => {
              row[header] = values[index] || ''
            })
            importData.push(row)
          }
        }
      } else if (file.name.endsWith('.json')) {
        // 解析JSON
        importData = JSON.parse(text)
      }

      if (importData.length === 0) {
        ElMessage.warning('文件中没有有效的文章数据')
        return
      }

      // 确认导入
      await ElMessageBox.confirm(
        `确定要导入 ${importData.length} 篇文章吗？`,
        '导入确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      // 批量创建文章
      let successCount = 0
      let errorCount = 0

      for (const item of importData) {
        try {
          const articleData = {
            title: item.标题 || item.title || '未命名文章',
            content: item.内容 || item.content || '',
            excerpt: item.摘要 || item.excerpt || '',
            status: (item.状态 === '已发布' || item.status === 'published') ? 'published' as const : 'draft' as const
          }

          await articleStore.createArticle(articleData)
          successCount++
        } catch (error) {
          console.error('导入文章失败:', error)
          errorCount++
        }
      }

      if (successCount > 0) {
        ElMessage.success(`成功导入 ${successCount} 篇文章${errorCount > 0 ? `，失败 ${errorCount} 篇` : ''}`)
        fetchArticles()
      } else {
        ElMessage.error('导入失败')
      }

    } catch (error) {
      console.error('解析文件失败:', error)
      ElMessage.error('文件格式不正确')
    }
  }

  input.click()
}

// 初始化数据
const initData = async () => {
  try {
    loading.value = true

    // 并行加载数据
    await Promise.all([
      fetchArticles(),
      categoryStore.fetchCategories(),
      tagStore.fetchTags()
    ])
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error('初始化数据失败')
  } finally {
    loading.value = false
  }
}

// 键盘快捷键
const handleKeydown = async (event: KeyboardEvent) => {
  // Ctrl/Cmd + N: 新建文章
  if ((event.ctrlKey || event.metaKey) && event.key === 'n') {
    event.preventDefault()
    if (await hasPermission('article.create')) {
      handleCreateArticle()
    }
  }

  // Ctrl/Cmd + E: 导出文章
  if ((event.ctrlKey || event.metaKey) && event.key === 'e') {
    event.preventDefault()
    if (await hasPermission('article.list')) {
      handleExportArticles()
    }
  }

  // F5: 刷新列表
  if (event.key === 'F5') {
    event.preventDefault()
    fetchArticles()
  }

  // Escape: 清除选择
  if (event.key === 'Escape') {
    selectedArticles.value = []
  }
}

// 组件挂载时初始化数据
onMounted(() => {
  initData()

  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeydown)
})

// 组件卸载时清理
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
.article-management {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面头部样式 */
.page-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  flex: 1;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.help-icon {
  color: #909399;
  cursor: help;
  font-size: 16px;
}

.help-icon:hover {
  color: #409eff;
}

.header-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

/* 统计卡片样式 */
.stats-section {
  margin-bottom: 20px;
}

.stats-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stats-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px 0;
}

.stats-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.published {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.draft {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.today {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-info {
  flex: 1;
}

.stats-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

/* 筛选卡片样式 */
.filter-card {
  margin-bottom: 20px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-content {
  padding: 8px 0;
}

.filter-form {
  margin-bottom: 16px;
}

.filter-form .el-form-item {
  margin-bottom: 16px;
}

.batch-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background-color: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 6px;
}

.batch-info {
  color: #1e40af;
  font-weight: 500;
  margin-right: auto;
}

/* 表格卡片样式 */
.table-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-container {
  min-height: 400px;
}

.article-table {
  width: 100%;
}

/* 文章信息样式 */
.article-info {
  padding: 8px 0;
}

.article-details {
  width: 100%;
}

.article-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.title-link {
  font-weight: 500;
  color: #303133;
  text-decoration: none;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.title-link:hover {
  color: #409eff;
}

.status-tag {
  flex-shrink: 0;
}

.article-excerpt {
  color: #606266;
  font-size: 12px;
  line-height: 1.4;
  margin-bottom: 8px;
  max-width: 250px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.article-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #909399;
}

/* 标签样式 */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  max-width: 180px;
}

.tag-item {
  margin: 0;
}

.no-tags {
  color: #c0c4cc;
  font-size: 12px;
  font-style: italic;
}

/* 时间样式 */
.publish-time,
.create-time {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #606266;
}

.not-published {
  color: #c0c4cc;
  font-size: 12px;
  font-style: italic;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.action-buttons .el-button {
  margin: 0;
  padding: 4px 8px;
}

.publish-btn {
  color: #67c23a;
}

.unpublish-btn {
  color: #e6a23c;
}

.delete-btn {
  color: #f56c6c;
}

/* 下拉菜单样式 */
.el-dropdown-menu__item.danger-item {
  color: #f56c6c;
}

.el-dropdown-menu__item.danger-item:hover {
  background-color: #fef0f0;
  color: #f56c6c;
}

.el-dropdown-menu__item .el-icon {
  margin-right: 8px;
}

/* 空状态样式 */
.empty-state {
  padding: 60px 0;
  text-align: center;
}

.empty-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: 16px;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: center;
  padding: 20px 0;
  border-top: 1px solid #ebeef5;
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: flex-start;
  }

  .article-excerpt {
    max-width: 200px;
  }

  .tags-container {
    max-width: 150px;
  }
}

@media (max-width: 768px) {
  .article-management {
    padding: 16px;
  }

  .header-content {
    padding: 16px;
  }

  .page-title {
    font-size: 20px;
  }

  .filter-form {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .filter-form .el-form-item {
    margin-bottom: 8px;
  }

  .filter-form .el-form-item__label {
    width: auto !important;
    margin-bottom: 4px;
  }

  .filter-form .el-form-item__content {
    margin-left: 0 !important;
  }

  .batch-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .batch-info {
    margin-right: 0;
    margin-bottom: 8px;
  }

  /* 隐藏部分表格列 */
  .article-table .el-table__header th:nth-child(4),
  .article-table .el-table__body td:nth-child(4),
  .article-table .el-table__header th:nth-child(5),
  .article-table .el-table__body td:nth-child(5) {
    display: none;
  }

  .article-meta {
    flex-direction: row;
    flex-wrap: wrap;
  }

  .action-buttons {
    flex-direction: column;
    align-items: stretch;
  }

  .action-buttons .el-button {
    justify-content: center;
    margin-bottom: 4px;
  }

  .pagination-container {
    padding: 16px 0;
  }

  .pagination-container .el-pagination {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .stats-section .el-col {
    margin-bottom: 16px;
  }

  .stats-content {
    gap: 12px;
  }

  .stats-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }

  .stats-value {
    font-size: 20px;
  }

  .article-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .title-link {
    max-width: none;
  }

  .article-excerpt {
    max-width: none;
  }

  .tags-container {
    max-width: none;
  }

  /* 进一步简化表格 */
  .article-table .el-table__header th:nth-child(3),
  .article-table .el-table__body td:nth-child(3) {
    display: none;
  }
}
</style>