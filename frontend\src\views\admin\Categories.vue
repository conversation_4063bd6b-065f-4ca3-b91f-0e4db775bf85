<template>
  <div class="categories-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon><FolderOpened /></el-icon>
            分类管理
          </h1>
          <p class="page-description">管理博客分类层级结构和内容组织</p>
        </div>
        <div class="header-actions">
          <el-button @click="refreshData" :loading="loading" icon="Refresh">
            刷新
          </el-button>
          <el-button @click="toggleExpandAll" icon="Sort">
            {{ isAllExpanded ? '折叠全部' : '展开全部' }}
          </el-button>
          <el-dropdown trigger="click">
            <el-button icon="Star">
              分类模板
              <el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="applyTemplate('blog')">
                  <el-icon><Document /></el-icon>
                  技术博客模板
                </el-dropdown-item>
                <el-dropdown-item @click="applyTemplate('portfolio')">
                  <el-icon><Picture /></el-icon>
                  作品集模板
                </el-dropdown-item>
                <el-dropdown-item @click="applyTemplate('business')">
                  <el-icon><OfficeBuilding /></el-icon>
                  企业网站模板
                </el-dropdown-item>
                <el-dropdown-item divided @click="showTemplateDialog">
                  <el-icon><Setting /></el-icon>
                  自定义模板
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-button type="primary" @click="showCreateDialog" icon="Plus">
            新建分类
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6">
          <el-card class="stat-card enhanced" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon total">
                <el-icon><FolderOpened /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.totalCategories || 0 }}</div>
                <div class="stat-label">总分类数</div>
                <div class="stat-description">系统中所有分类</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="12" :sm="6">
          <el-card class="stat-card enhanced" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon root">
                <el-icon><Folder /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.rootCategories || 0 }}</div>
                <div class="stat-label">根分类数</div>
                <div class="stat-description">顶级分类数量</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="12" :sm="6">
          <el-card class="stat-card enhanced" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon depth">
                <el-icon><Sort /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.maxDepth || 0 }}</div>
                <div class="stat-label">最深层级</div>
                <div class="stat-description">分类树最大深度</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="12" :sm="6">
          <el-card class="stat-card enhanced" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon average">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ (stats.averageArticlesPerCategory || 0).toFixed(1) }}</div>
                <div class="stat-label">平均文章数</div>
                <div class="stat-description">每个分类平均文章</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="filter-section">
      <el-card>
        <el-form :model="filterForm" inline>
          <el-form-item label="搜索分类">
            <el-input
              v-model="filterForm.search"
              placeholder="输入分类名称搜索"
              clearable
              @input="handleSearch"
              style="width: 200px"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="显示模式">
            <el-select v-model="filterForm.viewMode" @change="handleViewModeChange" style="width: 120px">
              <el-option label="树形视图" value="tree" />
              <el-option label="扁平列表" value="flat" />
            </el-select>
          </el-form-item>
          <el-form-item label="排序方式">
            <el-select v-model="filterForm.sortBy" @change="handleSort" style="width: 140px">
              <el-option label="按排序值" value="sort" />
              <el-option label="按名称" value="name" />
              <el-option label="按创建时间" value="createdAt" />
              <el-option label="按文章数量" value="articleCount" />
            </el-select>
          </el-form-item>
          <el-form-item label="筛选条件">
            <el-select v-model="filterForm.filter" @change="handleFilter" style="width: 120px">
              <el-option label="全部" value="all" />
              <el-option label="有文章" value="hasArticles" />
              <el-option label="无文章" value="noArticles" />
            </el-select>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 批量操作区域 -->
    <div class="batch-actions" v-if="selectedCategories.length > 0">
      <el-alert
        :title="`已选择 ${selectedCategories.length} 个分类`"
        type="info"
        :closable="false"
      >
        <template #default>
          <div class="batch-buttons">
            <el-button
              type="danger"
              size="small"
              @click="handleBatchDelete"
              :loading="batchDeleting"
            >
              批量删除
            </el-button>
            <el-button
              type="primary"
              size="small"
              @click="showBatchMoveDialog"
            >
              批量移动
            </el-button>
            <el-button size="small" @click="clearSelection">
              取消选择
            </el-button>
          </div>
        </template>
      </el-alert>
    </div>

    <!-- 分类列表表格 -->
    <div class="table-section">
      <el-card>
        <el-table
          ref="tableRef"
          v-loading="loading"
          :data="displayCategories"
          @selection-change="handleSelectionChange"
          row-key="id"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          :default-expand-all="false"
          stripe
          border
          style="width: 100%"
        >
          <!-- 选择列 -->
          <el-table-column type="selection" width="55" />

          <!-- 拖拽手柄列 -->
          <el-table-column label="" width="40" v-if="filterForm.viewMode === 'tree'">
            <template #default>
              <el-icon class="drag-handle" style="cursor: move; color: #ccc;">
                <Sort />
              </el-icon>
            </template>
          </el-table-column>
          
          <!-- 分类名称列 -->
          <el-table-column prop="name" label="分类名称" min-width="200">
            <template #default="{ row }">
              <div class="category-name-cell">
                <el-icon class="category-icon">
                  <FolderOpened v-if="row.children && row.children.length > 0" />
                  <Folder v-else />
                </el-icon>
                <span class="category-name">{{ row.name }}</span>
                <el-tag v-if="row.articleCount > 0" size="small" type="primary">
                  {{ row.articleCount }}篇
                </el-tag>
              </div>
            </template>
          </el-table-column>

          <!-- Slug列 -->
          <el-table-column prop="slug" label="Slug" min-width="150" show-overflow-tooltip />

          <!-- 描述列 -->
          <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip>
            <template #default="{ row }">
              <span class="text-gray-500">{{ row.description || '暂无描述' }}</span>
            </template>
          </el-table-column>

          <!-- 父分类列 -->
          <el-table-column prop="parent" label="父分类" width="150" v-if="filterForm.viewMode === 'flat'">
            <template #default="{ row }">
              <span v-if="row.parent">{{ row.parent.name }}</span>
              <span v-else class="text-gray-400">根分类</span>
            </template>
          </el-table-column>

          <!-- 排序值列 -->
          <el-table-column prop="sort" label="排序" width="80" sortable />

          <!-- 文章数量列 -->
          <el-table-column prop="articleCount" label="文章数" width="100" sortable>
            <template #default="{ row }">
              <el-link 
                v-if="row.articleCount > 0"
                :href="`/category/${row.slug}`"
                target="_blank"
                type="primary"
              >
                {{ row.articleCount }}
              </el-link>
              <span v-else class="text-gray-400">0</span>
            </template>
          </el-table-column>

          <!-- 创建时间列 -->
          <el-table-column prop="createdAt" label="创建时间" width="180" sortable>
            <template #default="{ row }">
              {{ formatDate(row.createdAt) }}
            </template>
          </el-table-column>

          <!-- 操作列 -->
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button 
                type="primary" 
                size="small" 
                @click="handleEdit(row)"
                icon="Edit"
              >
                编辑
              </el-button>
              <el-button 
                type="success" 
                size="small" 
                @click="handleAddChild(row)"
                icon="Plus"
              >
                添加子分类
              </el-button>
              <el-button 
                type="danger" 
                size="small" 
                @click="handleDelete(row)"
                icon="Delete"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 空状态 -->
        <el-empty v-if="!loading && displayCategories.length === 0" description="暂无分类数据">
          <el-button type="primary" @click="showCreateDialog">创建第一个分类</el-button>
        </el-empty>
      </el-card>
    </div>

    <!-- 分类编辑对话框 -->
    <CategoryEditDialog
      v-model="dialogVisible"
      :category="currentCategory"
      :mode="dialogMode"
      @success="handleDialogSuccess"
    />

    <!-- 批量移动对话框 -->
    <el-dialog
      v-model="batchMoveDialogVisible"
      title="批量移动分类"
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="batch-move-content">
        <p>将选中的 {{ selectedCategories.length }} 个分类移动到：</p>
        <CategorySelector
          v-model="batchMoveTargetId"
          :exclude-ids="selectedCategories.map(cat => cat.id)"
          placeholder="选择目标父分类（留空表示移动到根级别）"
          clearable
        />
        <div class="selected-categories">
          <h4>选中的分类：</h4>
          <el-tag
            v-for="category in selectedCategories"
            :key="category.id"
            style="margin: 2px;"
          >
            {{ category.name }}
          </el-tag>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="batchMoveDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleBatchMove"
            :loading="batchMoving"
          >
            确认移动
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox, ElTable } from 'element-plus'
import {
  Search, Plus, Edit, Delete, Refresh, Sort, FolderOpened, Folder, Document, QuestionFilled,
  ArrowDown, Star, Picture, OfficeBuilding, Setting
} from '@element-plus/icons-vue'
import { useCategoryStore } from '@/stores/category'
import type { Category } from '@/types/category'
import CategoryEditDialog from '@/components/admin/CategoryEditDialog.vue'
import Sortable from 'sortablejs'

// 响应式数据
const categoryStore = useCategoryStore()
const loading = ref(false)
const batchDeleting = ref(false)
const tableRef = ref<InstanceType<typeof ElTable>>()
const isAllExpanded = ref(false)
const sortableInstance = ref<Sortable | null>(null)
const isDragging = ref(false)

// 统计数据
const stats = ref({
  totalCategories: 0,
  rootCategories: 0,
  maxDepth: 0,
  averageArticlesPerCategory: 0
})

// 分类列表
const categories = ref<Category[]>([])
const selectedCategories = ref<Category[]>([])

// 筛选表单
const filterForm = reactive({
  search: '',
  viewMode: 'tree' as 'tree' | 'flat',
  sortBy: 'sort',
  filter: 'all'
})

// 对话框
const dialogVisible = ref(false)
const dialogMode = ref<'create' | 'edit'>('create')
const currentCategory = ref<Category | null>(null)

// 批量移动对话框
const batchMoveDialogVisible = ref(false)
const batchMoveTargetId = ref<number | null>(null)
const batchMoving = ref(false)

// 计算属性
const displayCategories = computed(() => {
  let result: Category[] = []
  
  if (filterForm.viewMode === 'tree') {
    result = categories.value.filter(cat => !cat.parentId) // 只显示根分类，子分类通过children属性显示
  } else {
    result = [...categories.value] // 扁平显示所有分类
  }

  // 搜索过滤
  if (filterForm.search) {
    const searchTerm = filterForm.search.toLowerCase()
    result = filterCategories(result, searchTerm)
  }

  // 条件过滤
  if (filterForm.filter === 'hasArticles') {
    result = result.filter(cat => (cat.articleCount || 0) > 0)
  } else if (filterForm.filter === 'noArticles') {
    result = result.filter(cat => (cat.articleCount || 0) === 0)
  }

  // 排序
  return sortCategories(result, filterForm.sortBy)
})

// 方法定义
const loadData = async () => {
  loading.value = true
  try {
    await categoryStore.fetchCategoryTree(true)
    await categoryStore.fetchCategoryStats(true, true)
    
    categories.value = categoryStore.categoryTree
    
    // 计算统计信息
    calculateStats()

    // 初始化拖拽功能
    await nextTick()
    initSortable()
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const calculateStats = () => {
  const allCategories = flattenCategories(categories.value)
  const totalArticles = allCategories.reduce((sum, cat) => sum + (cat.articleCount || 0), 0)
  
  stats.value = {
    totalCategories: allCategories.length,
    rootCategories: categories.value.length,
    maxDepth: calculateMaxDepth(categories.value),
    averageArticlesPerCategory: allCategories.length > 0 ? totalArticles / allCategories.length : 0
  }
}

const flattenCategories = (cats: Category[], depth = 0): Category[] => {
  let result: Category[] = []
  for (const cat of cats) {
    result.push({ ...cat, level: depth })
    if (cat.children && cat.children.length > 0) {
      result.push(...flattenCategories(cat.children, depth + 1))
    }
  }
  return result
}

const calculateMaxDepth = (cats: Category[], currentDepth = 1): number => {
  let maxDepth = currentDepth
  for (const cat of cats) {
    if (cat.children && cat.children.length > 0) {
      const childDepth = calculateMaxDepth(cat.children, currentDepth + 1)
      maxDepth = Math.max(maxDepth, childDepth)
    }
  }
  return maxDepth
}

const filterCategories = (cats: Category[], searchTerm: string): Category[] => {
  return cats.filter(cat => {
    const matchesSearch = cat.name.toLowerCase().includes(searchTerm) ||
                         cat.slug.toLowerCase().includes(searchTerm) ||
                         (cat.description && cat.description.toLowerCase().includes(searchTerm))
    
    if (matchesSearch) return true
    
    // 如果子分类匹配，也显示父分类
    if (cat.children && cat.children.length > 0) {
      const filteredChildren = filterCategories(cat.children, searchTerm)
      if (filteredChildren.length > 0) {
        cat.children = filteredChildren
        return true
      }
    }
    
    return false
  })
}

const sortCategories = (cats: Category[], sortBy: string): Category[] => {
  const sorted = [...cats].sort((a, b) => {
    switch (sortBy) {
      case 'name':
        return a.name.localeCompare(b.name)
      case 'createdAt':
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      case 'articleCount':
        return (b.articleCount || 0) - (a.articleCount || 0)
      case 'sort':
      default:
        return (a.sort || 0) - (b.sort || 0)
    }
  })

  // 递归排序子分类
  sorted.forEach(cat => {
    if (cat.children && cat.children.length > 0) {
      cat.children = sortCategories(cat.children, sortBy)
    }
  })

  return sorted
}

const refreshData = () => {
  loadData()
}

const toggleExpandAll = () => {
  isAllExpanded.value = !isAllExpanded.value
  if (tableRef.value) {
    const allCategories = flattenCategories(categories.value)
    allCategories.forEach(cat => {
      if (cat.children && cat.children.length > 0) {
        tableRef.value?.toggleRowExpansion(cat, isAllExpanded.value)
      }
    })
  }
}

const handleSearch = () => {
  // 搜索时重新计算显示数据
}

const handleViewModeChange = async () => {
  // 视图模式切换时重新加载数据
  if (filterForm.viewMode === 'flat') {
    await categoryStore.fetchCategoryFlatList(true)
  }

  // 重新初始化拖拽功能
  await nextTick()
  initSortable()
}

const handleSort = () => {
  // 排序时重新计算显示数据
}

const handleFilter = () => {
  // 筛选时重新计算显示数据
}

const handleSelectionChange = (selection: Category[]) => {
  selectedCategories.value = selection
}

const clearSelection = () => {
  tableRef.value?.clearSelection()
}

const showCreateDialog = () => {
  currentCategory.value = null
  dialogMode.value = 'create'
  dialogVisible.value = true
}

const handleEdit = (category: Category) => {
  currentCategory.value = category
  dialogMode.value = 'edit'
  dialogVisible.value = true
}

const handleAddChild = (category: Category) => {
  currentCategory.value = { ...category, parentId: category.id } as any
  dialogMode.value = 'create'
  dialogVisible.value = true
}

const handleDelete = async (category: Category) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除分类 "${category.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await categoryStore.deleteCategory(category.id)
    ElMessage.success('删除成功')
    await loadData()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedCategories.value.length} 个分类吗？此操作不可恢复。`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    batchDeleting.value = true
    const categoryIds = selectedCategories.value.map(cat => cat.id)
    await categoryStore.deleteCategories(categoryIds)

    ElMessage.success(`成功删除 ${selectedCategories.value.length} 个分类`)
    clearSelection()
    await loadData()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  } finally {
    batchDeleting.value = false
  }
}

const handleDialogSuccess = async () => {
  dialogVisible.value = false
  await loadData()
}

const showBatchMoveDialog = () => {
  batchMoveDialogVisible.value = true
  batchMoveTargetId.value = null
}

const handleBatchMove = async () => {
  if (!batchMoveTargetId.value) {
    ElMessage.warning('请选择目标分类')
    return
  }

  try {
    batchMoving.value = true

    for (const category of selectedCategories.value) {
      // 检查是否会造成循环引用
      if (batchMoveTargetId.value === category.id) {
        ElMessage.error(`不能将分类 "${category.name}" 移动到自己下面`)
        continue
      }

      await categoryStore.updateCategory(category.id, {
        parentId: batchMoveTargetId.value
      })
    }

    ElMessage.success(`成功移动 ${selectedCategories.value.length} 个分类`)
    batchMoveDialogVisible.value = false
    clearSelection()
    await loadData()
  } catch (error: any) {
    ElMessage.error('批量移动失败')
  } finally {
    batchMoving.value = false
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 分类模板功能
const categoryTemplates = {
  blog: [
    { name: '前端开发', children: ['Vue.js', 'React', 'JavaScript', 'CSS'] },
    { name: '后端开发', children: ['Node.js', 'Python', 'Java', 'Go'] },
    { name: '数据库', children: ['MySQL', 'MongoDB', 'Redis'] },
    { name: '工具与部署', children: ['Docker', 'Git', 'CI/CD'] },
    { name: '算法与数据结构', children: [] },
    { name: '系统设计', children: [] }
  ],
  portfolio: [
    { name: '网页设计', children: ['企业网站', '电商平台', '个人博客'] },
    { name: '移动应用', children: ['iOS应用', 'Android应用', '小程序'] },
    { name: '品牌设计', children: ['Logo设计', 'VI设计', '包装设计'] },
    { name: '插画作品', children: [] },
    { name: '摄影作品', children: [] }
  ],
  business: [
    { name: '公司介绍', children: ['企业文化', '发展历程', '团队介绍'] },
    { name: '产品服务', children: ['核心产品', '解决方案', '技术支持'] },
    { name: '新闻动态', children: ['公司新闻', '行业资讯', '活动公告'] },
    { name: '客户案例', children: [] },
    { name: '联系我们', children: [] }
  ]
}

const applyTemplate = async (templateType: keyof typeof categoryTemplates) => {
  try {
    await ElMessageBox.confirm(
      `确定要应用${templateType === 'blog' ? '技术博客' : templateType === 'portfolio' ? '作品集' : '企业网站'}模板吗？这将创建预设的分类结构。`,
      '应用分类模板',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    const template = categoryTemplates[templateType]
    let successCount = 0

    for (const category of template) {
      try {
        // 创建父分类
        const parentCategory = await categoryStore.createCategory({
          name: category.name,
          description: `${category.name}相关内容`
        })

        // 创建子分类
        for (const childName of category.children) {
          await categoryStore.createCategory({
            name: childName,
            parentId: parentCategory.id,
            description: `${childName}相关内容`
          })
        }

        successCount++
      } catch (error) {
        console.error(`创建分类 ${category.name} 失败:`, error)
      }
    }

    ElMessage.success(`成功应用模板，创建了 ${successCount} 个主分类`)
    await loadData()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('应用模板失败')
    }
  }
}

const showTemplateDialog = () => {
  ElMessage.info('自定义模板功能开发中...')
}

// 拖拽排序相关方法
const initSortable = async () => {
  await nextTick()

  if (filterForm.viewMode !== 'tree') {
    return // 只在树形视图下启用拖拽
  }

  const tableBody = document.querySelector('.el-table__body tbody')
  if (!tableBody) return

  // 销毁之前的实例
  if (sortableInstance.value) {
    sortableInstance.value.destroy()
  }

  sortableInstance.value = Sortable.create(tableBody as HTMLElement, {
    animation: 150,
    ghostClass: 'sortable-ghost',
    chosenClass: 'sortable-chosen',
    dragClass: 'sortable-drag',
    handle: '.drag-handle',
    onStart: () => {
      isDragging.value = true
    },
    onEnd: async (evt) => {
      isDragging.value = false
      const { oldIndex, newIndex } = evt

      if (oldIndex !== undefined && newIndex !== undefined && oldIndex !== newIndex) {
        await handleDragSort(oldIndex, newIndex)
      }
    }
  })
}

const handleDragSort = async (oldIndex: number, newIndex: number) => {
  try {
    const currentData = [...displayCategories.value]
    const movedItem = currentData[oldIndex]

    // 重新排列数组
    currentData.splice(oldIndex, 1)
    currentData.splice(newIndex, 0, movedItem)

    // 更新排序值
    const updates = currentData.map((item, index) => ({
      id: item.id,
      sort: index
    }))

    // 这里应该调用批量更新API，暂时简化处理
    for (const update of updates) {
      await categoryStore.updateCategory(update.id, { sort: update.sort })
    }

    ElMessage.success('排序更新成功')
    await loadData()
  } catch (error) {
    ElMessage.error('排序更新失败')
    await loadData() // 重新加载数据恢复原状态
  }
}

// 键盘快捷键处理
const handleKeydown = (event: KeyboardEvent) => {
  // Ctrl/Cmd + N: 新建分类
  if ((event.ctrlKey || event.metaKey) && event.key === 'n') {
    event.preventDefault()
    showCreateDialog()
  }

  // Delete: 删除选中的分类
  if (event.key === 'Delete' && selectedCategories.value.length > 0) {
    event.preventDefault()
    handleBatchDelete()
  }

  // F5: 刷新数据
  if (event.key === 'F5') {
    event.preventDefault()
    refreshData()
  }

  // Escape: 清除选择
  if (event.key === 'Escape') {
    clearSelection()
  }
}

// 生命周期
onMounted(() => {
  loadData()

  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  // 清理键盘事件监听
  document.removeEventListener('keydown', handleKeydown)

  // 清理拖拽实例
  if (sortableInstance.value) {
    sortableInstance.value.destroy()
  }
})
</script>

<style scoped>
.categories-management {
  padding: 20px;
  background-color: var(--el-bg-color-page);
  min-height: calc(100vh - 60px);
}

.page-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left {
  flex: 1;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-description {
  margin: 0;
  color: var(--el-text-color-secondary);
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.help-icon {
  color: var(--el-text-color-placeholder);
  cursor: help;
  font-size: 16px;
  transition: color 0.2s;
}

.help-icon:hover {
  color: var(--el-color-primary);
}

.header-actions {
  display: flex;
  gap: 12px;
}

.stats-section {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
  transition: all 0.3s ease;
}

.stat-card.enhanced {
  border: none;
  border-radius: 12px;
  overflow: hidden;
  background: white;
  transition: all 0.3s ease;
}

.stat-card.enhanced:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 20px;
  gap: 16px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  flex-shrink: 0;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.root {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.depth {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.average {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
  text-align: left;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: var(--el-text-color-primary);
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 2px;
}

.stat-description {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  line-height: 1.2;
}

.filter-section {
  margin-bottom: 20px;
}

.batch-actions {
  margin-bottom: 20px;
}

.batch-buttons {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.table-section {
  margin-bottom: 20px;
}

.category-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.category-icon {
  color: var(--el-color-primary);
  font-size: 16px;
}

.category-name {
  font-weight: 500;
}

/* 拖拽相关样式 */
.drag-handle {
  cursor: move;
  color: #ccc;
  transition: color 0.2s;
}

.drag-handle:hover {
  color: var(--el-color-primary);
}

.sortable-ghost {
  opacity: 0.5;
  background-color: var(--el-color-primary-light-9);
}

.sortable-chosen {
  background-color: var(--el-color-primary-light-8);
}

.sortable-drag {
  background-color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 批量移动对话框样式 */
.batch-move-content {
  padding: 10px 0;
}

.batch-move-content p {
  margin-bottom: 15px;
  font-weight: 500;
}

.selected-categories {
  margin-top: 20px;
  padding: 15px;
  background-color: var(--el-bg-color-page);
  border-radius: 6px;
}

.selected-categories h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: var(--el-text-color-primary);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: flex-start;
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  .categories-management {
    padding: 16px;
  }

  .header-content {
    padding: 16px;
  }

  .page-title {
    font-size: 20px;
  }

  .header-actions {
    justify-content: center;
    flex-wrap: wrap;
    gap: 8px;
  }

  .header-actions .el-button {
    flex: 1;
    min-width: 120px;
  }

  /* 统计卡片在移动端的优化 */
  .stat-content {
    padding: 16px;
    gap: 12px;
  }

  .stat-icon {
    width: 48px;
    height: 48px;
    font-size: 20px;
  }

  .stat-value {
    font-size: 24px;
  }

  .stat-label {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .stats-section :deep(.el-col) {
    margin-bottom: 10px;
  }

  .filter-section :deep(.el-form) {
    flex-direction: column;
  }

  .filter-section :deep(.el-form-item) {
    margin-right: 0;
    margin-bottom: 10px;
  }

  .header-actions {
    flex-direction: column;
  }
}
</style>
